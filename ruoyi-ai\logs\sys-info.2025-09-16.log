2025-09-16 09:21:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-16 09:21:07 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 17152 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-09-16 09:21:07 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-09-16 09:21:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-16 09:21:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-09-16 09:21:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-09-16 09:21:20 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@729eef5f
2025-09-16 09:21:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-09-16 09:21:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-09-16 09:21:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-16 09:21:23 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 09:21:24 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-09-16 09:21:24 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-09-16 09:21:24 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 09:21:24 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 09:21:26 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 09:21:27 [HttpClient-1-Worker-2] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-09-16 09:21:30 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-09-16 09:21:30 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-09-16 09:21:30 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-09-16 09:21:33 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T01:21:33.460Z] Starting Search1API MCP server
2025-09-16 09:21:33 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T01:21:33.461Z] Creating Search1API MCP server
2025-09-16 09:21:33 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T01:21:33.463Z] Server started successfully
2025-09-16 09:21:33 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T01:21:33.463Z] Server started successfully
2025-09-16 09:21:33 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-09-16 09:21:39 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-16 09:21:39 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-09-16 09:21:39 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-16 09:21:39 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-16 09:21:39 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 33.433 seconds (process running for 35.087)
2025-09-16 09:21:39 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-09-16 09:21:39 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-09-16 09:21:39 [RMI TCP Connection(7)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-16 09:24:57 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:24:57 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:24:57 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:25:04 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:25:04 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:25:26 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:25:28 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:30:56 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:38:08 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:38:09 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:38:09 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-09-16 09:38:09 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-09-16 09:38:09 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 2725
2025-09-16 09:38:09 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-09-16 09:38:09 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (10053ms)
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Wed, 10 Sep 2025 23:15:16 GMT
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-09-16 09:38:19 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-09-16 09:39:00 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-09-16 09:51:35 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:51:35 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-09-16 09:51:35 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-09-16 09:51:35 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 12269
2025-09-16 09:51:35 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-09-16 09:51:35 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (10057ms)
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Wed, 10 Sep 2025 23:28:43 GMT
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-09-16 09:51:45 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-09-16 09:52:17 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-09-16 09:57:13 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:57:13 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:57:13 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:57:15 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:57:15 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:58:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:58:13 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:58:16 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:58:16 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 09:58:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-09-16 09:58:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-09-16 09:58:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 2725
2025-09-16 09:58:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-09-16 09:58:16 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (10106ms)
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Wed, 10 Sep 2025 23:35:23 GMT
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-09-16 09:58:26 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-09-16 09:59:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-09-16 10:00:43 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 10:00:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-09-16 10:00:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-09-16 10:00:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 12954
2025-09-16 10:00:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-09-16 10:00:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (10087ms)
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Wed, 10 Sep 2025 23:37:50 GMT
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-09-16 10:00:53 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-09-16 10:01:17 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-09-16 10:12:34 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 10:12:34 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-09-16 10:12:34 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-09-16 10:12:34 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 17777
2025-09-16 10:12:34 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-09-16 10:12:34 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (10085ms)
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Wed, 10 Sep 2025 23:49:42 GMT
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-09-16 10:12:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-09-16 10:13:35 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-09-16 10:16:42 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 10:16:42 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-09-16 10:16:42 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-09-16 10:16:42 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 22803
2025-09-16 10:16:42 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-09-16 10:16:42 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (1065ms)
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Wed, 10 Sep 2025 23:53:40 GMT
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-09-16 10:16:43 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-09-16 10:17:44 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 10:17:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-09-16 10:17:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-09-16 10:17:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 22803
2025-09-16 10:17:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-09-16 10:17:44 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (10042ms)
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Wed, 10 Sep 2025 23:54:51 GMT
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-09-16 10:17:54 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-09-16 10:18:08 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-09-16 10:27:01 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 10:27:01 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> POST http://192.168.241.130:3000/api/v1/chat/completions
2025-09-16 10:27:01 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: application/json; charset=utf-8
2025-09-16 10:27:01 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Length: 27591
2025-09-16 10:27:01 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Accept: text/event-stream
2025-09-16 10:27:01 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:--> END POST
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- 200 OK http://192.168.241.130:3000/api/v1/chat/completions (10063ms)
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Frame-Options: DENY
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Content-Type-Options: nosniff
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-XSS-Protection: 1; mode=block
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Referrer-Policy: strict-origin-when-cross-origin
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Permissions-Policy: geolocation=(self), microphone=(self), camera=(self)
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Connection: keep-alive
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Content-Type: text/event-stream;charset=utf-8
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Access-Control-Allow-Origin: *
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:X-Accel-Buffering: no
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Cache-Control: no-cache, no-transform
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Date: Thu, 11 Sep 2025 00:04:08 GMT
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:Transfer-Encoding: chunked
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.c.o.interceptor.OpenAILogger - OkHttp-------->:<-- END HTTP
2025-09-16 10:27:11 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接成功
2025-09-16 10:27:33 [OkHttp http://192.168.241.130:3000/...] INFO  o.r.c.l.FastGPTSSEEventSourceListener - FastGPT  sse连接关闭
2025-09-16 10:32:44 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-16 10:32:44 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-16 10:32:46 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-09-16 10:32:46 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-09-16 10:32:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-09-16 10:32:46 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-09-16 10:32:46 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-09-16 10:32:46 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-09-16 14:23:30 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-16 14:23:30 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 2492 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-09-16 14:23:30 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-09-16 14:23:40 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-16 14:23:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-09-16 14:23:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-09-16 14:23:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@46c7e6c0
2025-09-16 14:23:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-09-16 14:23:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-09-16 14:23:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-16 14:23:45 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 14:23:45 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-09-16 14:23:45 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-09-16 14:23:46 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 14:23:46 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 14:23:47 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 14:23:48 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-09-16 14:23:52 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-09-16 14:23:52 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-09-16 14:23:52 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-09-16 14:23:55 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T06:23:55.214Z] Starting Search1API MCP server
2025-09-16 14:23:55 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T06:23:55.216Z] Creating Search1API MCP server
2025-09-16 14:23:55 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T06:23:55.217Z] Server started successfully
2025-09-16 14:23:55 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T06:23:55.217Z] Server started successfully
2025-09-16 14:23:55 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-09-16 14:24:00 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-16 14:24:00 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-09-16 14:24:00 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-16 14:24:01 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-16 14:24:01 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 31.776 seconds (process running for 33.304)
2025-09-16 14:24:01 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-09-16 14:24:01 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-09-16 14:24:01 [RMI TCP Connection(1)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-16 14:29:23 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:29:23 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:29:24 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:29:30 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:29:30 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:29:35 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:29:40 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:29:49 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:35:52 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:45:59 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:46:10 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 14:53:56 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-16 14:53:56 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-16 14:53:58 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-09-16 14:53:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-09-16 14:53:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-09-16 14:53:58 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-09-16 14:53:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-09-16 14:53:58 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-09-16 14:59:14 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-16 14:59:14 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 19716 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-09-16 14:59:14 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-09-16 14:59:20 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-16 14:59:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-09-16 14:59:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-09-16 14:59:21 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@763a72da
2025-09-16 14:59:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-09-16 14:59:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-09-16 14:59:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-16 14:59:24 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 14:59:24 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-09-16 14:59:24 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-09-16 14:59:25 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 14:59:25 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 14:59:26 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 14:59:27 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-09-16 14:59:29 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-09-16 14:59:29 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-09-16 14:59:29 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-09-16 14:59:31 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T06:59:31.591Z] Starting Search1API MCP server
2025-09-16 14:59:31 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T06:59:31.592Z] Creating Search1API MCP server
2025-09-16 14:59:31 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T06:59:31.593Z] Server started successfully
2025-09-16 14:59:31 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T06:59:31.593Z] Server started successfully
2025-09-16 14:59:31 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-09-16 14:59:32 [main] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-09-16 14:59:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-09-16 14:59:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-09-16 14:59:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-09-16 14:59:32 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-09-16 14:59:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-09-16 15:11:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-16 15:11:55 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 17820 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-09-16 15:11:55 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-09-16 15:12:03 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-16 15:12:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-09-16 15:12:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-09-16 15:12:04 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2605433
2025-09-16 15:12:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-09-16 15:12:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-09-16 15:12:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-16 15:12:07 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 15:12:08 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-09-16 15:12:08 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-09-16 15:12:08 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 15:12:08 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 15:12:10 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 15:12:10 [HttpClient-1-Worker-2] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-09-16 15:12:13 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-09-16 15:12:13 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-09-16 15:12:13 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-09-16 15:12:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T07:12:15.922Z] Starting Search1API MCP server
2025-09-16 15:12:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T07:12:15.924Z] Creating Search1API MCP server
2025-09-16 15:12:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T07:12:15.925Z] Server started successfully
2025-09-16 15:12:15 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T07:12:15.925Z] Server started successfully
2025-09-16 15:12:15 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-09-16 15:12:20 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-16 15:12:20 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-09-16 15:12:20 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-16 15:12:21 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-16 15:12:21 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 26.181 seconds (process running for 27.504)
2025-09-16 15:12:21 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-09-16 15:12:21 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-09-16 15:12:21 [RMI TCP Connection(5)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-16 15:13:22 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:13:22 [XNIO-1 task-2] INFO  o.r.s.s.impl.ContextServiceImpl - 上下文不存在或已过期: key=ai_context_748196384439812096_748196384439812096
2025-09-16 15:13:23 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:19:37 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:26:32 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:26:33 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:26:33 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:26:33 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:30:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:30:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:30:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:30:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:30:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:54 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:43:54 [XNIO-1 task-2] INFO  o.r.s.s.impl.ContextServiceImpl - 上下文不存在或已过期: key=ai_context_748196384439812096_748196384439812096
2025-09-16 15:43:54 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:44:14 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:15 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:15 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:15 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:15 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:15 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:50 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:50 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:50 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:51 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:49:51 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:25 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:25 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:25 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:25 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:25 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:59 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:59 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:59 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:59 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:50:59 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:27 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:27 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:27 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:27 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:27 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:42 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:42 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:42 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:43 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:52:43 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:12 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:12 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:13 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:13 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:26 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:27 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:27 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:53:41 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-16 15:53:41 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-16 15:53:43 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-09-16 15:53:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-09-16 15:53:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-09-16 15:53:43 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-09-16 15:53:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-09-16 15:53:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-09-16 15:58:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-16 15:58:17 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 18752 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-09-16 15:58:17 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-09-16 15:58:23 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-16 15:58:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-09-16 15:58:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-09-16 15:58:24 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@ab11e76
2025-09-16 15:58:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-09-16 15:58:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-09-16 15:58:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-16 15:58:26 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 15:58:27 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-09-16 15:58:27 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-09-16 15:58:27 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 15:58:27 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 15:58:29 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 15:58:29 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-09-16 15:58:32 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-09-16 15:58:32 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-09-16 15:58:32 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-09-16 15:58:34 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T07:58:34.202Z] Starting Search1API MCP server
2025-09-16 15:58:34 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T07:58:34.204Z] Creating Search1API MCP server
2025-09-16 15:58:34 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T07:58:34.205Z] Server started successfully
2025-09-16 15:58:34 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T07:58:34.205Z] Server started successfully
2025-09-16 15:58:34 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-09-16 15:58:38 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-16 15:58:38 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-09-16 15:58:38 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-16 15:58:38 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-16 15:58:39 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 22.399 seconds (process running for 23.596)
2025-09-16 15:58:39 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-09-16 15:58:39 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-09-16 15:58:39 [RMI TCP Connection(6)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-16 15:58:54 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:58:54 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:58:54 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:58:55 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:58:55 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:59:19 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:59:19 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 15:59:19 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:03:23 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:03:23 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:03:24 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:03:25 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:03:25 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:03:25 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:03:25 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:04:34 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:04:34 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:04:39 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:05:43 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:05:43 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:05:47 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:05:47 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:05:52 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:05:52 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:03 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:03 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:07 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:07 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:07 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:07 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:07 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:07 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:06:07 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:15 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:15 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:15 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:16 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:16 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:16 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:16 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:32 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:32 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:32 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:56 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:56 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:56 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:07:58 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:08:13 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:08:13 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:08:13 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:11:18 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-09-16 16:11:18 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-16 16:11:20 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-09-16 16:11:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-09-16 16:11:20 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-09-16 16:11:20 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-09-16 16:11:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-09-16 16:11:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-09-16 16:11:30 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-09-16 16:11:30 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 20716 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-09-16 16:11:30 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-09-16 16:11:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-09-16 16:11:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-09-16 16:11:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-09-16 16:11:37 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3da8a302
2025-09-16 16:11:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-09-16 16:11:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-09-16 16:11:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-16 16:11:40 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 16:11:40 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-09-16 16:11:40 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-09-16 16:11:40 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 16:11:41 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-09-16 16:11:42 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-09-16 16:11:42 [HttpClient-1-Worker-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=ruoyi-mcp-serve, version=1.0.0] and Instructions null
2025-09-16 16:11:45 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Secure MCP Filesystem Server running on stdio
2025-09-16 16:11:45 [pool-3-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=null, tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=secure-filesystem-server, version=0.2.0] and Instructions null
2025-09-16 16:11:45 [pool-6-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: Client does not support MCP Roots, using allowed directories set from server args: [ 'D:\\ruoyi-mcp' ]
2025-09-16 16:11:47 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T08:11:47.360Z] Starting Search1API MCP server
2025-09-16 16:11:47 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T08:11:47.362Z] Creating Search1API MCP server
2025-09-16 16:11:47 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T08:11:47.363Z] Server started successfully
2025-09-16 16:11:47 [pool-9-thread-1] INFO  i.m.c.transport.StdioClientTransport - STDERR Message received: [2025-09-16T08:11:47.363Z] Server started successfully
2025-09-16 16:11:47 [pool-7-thread-1] INFO  i.m.client.McpAsyncClient - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[experimental=null, logging=null, prompts=null, resources=ResourceCapabilities[subscribe=null, listChanged=null], tools=ToolCapabilities[listChanged=null]], Info: Implementation[name=search1api-server, version=1.0.0] and Instructions null
2025-09-16 16:11:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-09-16 16:11:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-09-16 16:11:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-09-16 16:11:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-09-16 16:11:52 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 22.153 seconds (process running for 23.418)
2025-09-16 16:11:52 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-09-16 16:11:52 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-09-16 16:11:52 [RMI TCP Connection(3)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-16 16:12:14 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:12:14 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:12:14 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:12:16 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:12:28 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:12:28 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:12:28 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:17 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:17 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:17 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:19 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:36 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:36 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:36 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:42 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:42 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:42 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:45 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:48 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:13:48 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:14:01 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:14:02 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:14:38 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:14:38 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:14:38 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:17:17 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:17:17 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:17:17 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:17:18 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:17:21 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:19:53 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:19:53 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:19:53 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:19:55 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:19:55 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:20:28 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:20:32 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:20:38 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:22:45 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:22:47 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:22:59 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:23:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:23:41 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:44 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:44 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:44 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:46 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:46 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:48 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:49 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:55 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:24:55 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:26:11 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:26:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:26:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:24 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:24 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:24 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:24 [XNIO-1 task-5] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:26 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:26 [XNIO-1 task-5] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:44 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:44 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:50 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:50 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-09-16 16:34:50 [XNIO-1 task-5] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
