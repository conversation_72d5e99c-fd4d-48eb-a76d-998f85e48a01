import {
  toolbar_default
} from "./chunk-HW6W5QNU.js";
import {
  emits_default,
  props_default,
  table_default
} from "./chunk-RSRWDDLK.js";
import {
  getDomNode,
  getOffsetHeight,
  getPaddingTopBottomSize
} from "./chunk-YB3DXOVJ.js";
import {
  errLog,
  getSlotVNs
} from "./chunk-X4FLHRLX.js";
import {
  getLastZIndex,
  isEnableConf,
  nextZIndex
} from "./chunk-EKP7TM2V.js";
import {
  VxeUI,
  require_xe_utils
} from "./chunk-BCXSQRR2.js";
import "./chunk-PJ5U4TG7.js";
import {
  computed,
  createCommentVNode,
  defineComponent,
  h,
  nextTick,
  onMounted,
  onUnmounted,
  provide,
  reactive,
  ref,
  watch
} from "./chunk-GE6DY3YU.js";
import "./chunk-KT3WABTJ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/grid/src/grid.js
var import_xe_utils = __toESM(require_xe_utils());
var { getConfig, getI18n, commands, hooks, useFns, createEvent, globalEvents, GLOBAL_EVENT_KEYS } = VxeUI;
var tableComponentPropKeys = Object.keys(props_default);
var tableComponentMethodKeys = ["clearAll", "syncData", "updateData", "loadData", "reloadData", "reloadRow", "loadColumn", "reloadColumn", "getRowNode", "getColumnNode", "getRowIndex", "getVTRowIndex", "getVMRowIndex", "getColumnIndex", "getVTColumnIndex", "getVMColumnIndex", "setRow", "createData", "createRow", "revertData", "clearData", "isInsertByRow", "isUpdateByRow", "getColumns", "getColumnById", "getColumnByField", "getTableColumn", "getFullColumns", "getData", "getCheckboxRecords", "getParentRow", "getTreeParentRow", "getRowSeq", "getRowById", "getRowid", "getTableData", "getFullData", "setColumnFixed", "clearColumnFixed", "setColumnWidth", "getColumnWidth", "hideColumn", "showColumn", "resetColumn", "refreshColumn", "refreshScroll", "recalculate", "closeTooltip", "isAllCheckboxChecked", "isAllCheckboxIndeterminate", "getCheckboxIndeterminateRecords", "setCheckboxRow", "setCheckboxRowKey", "isCheckedByCheckboxRow", "isCheckedByCheckboxRowKey", "isIndeterminateByCheckboxRow", "isIndeterminateByCheckboxRowKey", "toggleCheckboxRow", "setAllCheckboxRow", "getRadioReserveRecord", "clearRadioReserve", "getCheckboxReserveRecords", "clearCheckboxReserve", "toggleAllCheckboxRow", "clearCheckboxRow", "setCurrentRow", "isCheckedByRadioRow", "isCheckedByRadioRowKey", "setRadioRow", "setRadioRowKey", "clearCurrentRow", "clearRadioRow", "getCurrentRecord", "getRadioRecord", "getCurrentColumn", "setCurrentColumn", "clearCurrentColumn", "setPendingRow", "togglePendingRow", "getPendingRecords", "clearPendingRow", "sort", "setSort", "clearSort", "isSort", "getSortColumns", "closeFilter", "isFilter", "isActiveFilterByColumn", "isRowExpandLoaded", "clearRowExpandLoaded", "reloadRowExpand", "reloadRowExpand", "toggleRowExpand", "setAllRowExpand", "setRowExpand", "isExpandByRow", "isRowExpandByRow", "clearRowExpand", "clearRowExpandReserve", "getRowExpandRecords", "getTreeExpandRecords", "isTreeExpandLoaded", "clearTreeExpandLoaded", "reloadTreeExpand", "reloadTreeChilds", "toggleTreeExpand", "setAllTreeExpand", "setTreeExpand", "isTreeExpandByRow", "clearTreeExpand", "clearTreeExpandReserve", "getScroll", "scrollTo", "scrollToRow", "scrollToColumn", "clearScroll", "updateFooter", "updateStatus", "setMergeCells", "removeInsertRow", "removeMergeCells", "getMergeCells", "clearMergeCells", "setMergeFooterItems", "removeMergeFooterItems", "getMergeFooterItems", "clearMergeFooterItems", "getCustomStoreData", "openTooltip", "getCellLabel", "getCellElement", "focus", "blur", "connect"];
var gridComponentEmits = [
  ...emits_default,
  "page-change",
  "form-submit",
  "form-submit-invalid",
  "form-reset",
  "form-collapse",
  "form-toggle-collapse",
  "proxy-query",
  "proxy-delete",
  "proxy-save",
  "toolbar-button-click",
  "toolbar-tool-click",
  "zoom"
];
var grid_default = defineComponent({
  name: "VxeGrid",
  props: Object.assign(Object.assign({}, props_default), { layouts: Array, columns: Array, pagerConfig: Object, proxyConfig: Object, toolbarConfig: Object, formConfig: Object, zoomConfig: Object, size: {
    type: String,
    default: () => getConfig().grid.size || getConfig().size
  } }),
  emits: gridComponentEmits,
  setup(props, context) {
    var _a;
    const { slots, emit } = context;
    const xID = import_xe_utils.default.uniqueId();
    const VxeUIFormComponent = VxeUI.getComponent("VxeForm");
    const VxeUIPagerComponent = VxeUI.getComponent("VxePager");
    const { computeSize } = useFns.useSize(props);
    const reactData = reactive({
      tableLoading: false,
      proxyInited: false,
      isZMax: false,
      tableData: [],
      filterData: [],
      formData: {},
      sortData: [],
      tZindex: 0,
      tablePage: {
        total: 0,
        pageSize: ((_a = getConfig().pager) === null || _a === void 0 ? void 0 : _a.pageSize) || 10,
        currentPage: 1
      }
    });
    const refElem = ref();
    const refTable = ref();
    const refForm = ref();
    const refToolbar = ref();
    const refPager = ref();
    const refFormWrapper = ref();
    const refToolbarWrapper = ref();
    const refTopWrapper = ref();
    const refBottomWrapper = ref();
    const refPagerWrapper = ref();
    const extendTableMethods = (methodKeys) => {
      const funcs = {};
      methodKeys.forEach((name) => {
        funcs[name] = (...args) => {
          const $xeTable = refTable.value;
          if ($xeTable && $xeTable[name]) {
            return $xeTable[name](...args);
          }
        };
      });
      return funcs;
    };
    const gridExtendTableMethods = extendTableMethods(tableComponentMethodKeys);
    tableComponentMethodKeys.forEach((name) => {
      gridExtendTableMethods[name] = (...args) => {
        const $xeTable = refTable.value;
        if ($xeTable && $xeTable[name]) {
          return $xeTable && $xeTable[name](...args);
        }
      };
    });
    const computeProxyOpts = computed(() => {
      return import_xe_utils.default.merge({}, import_xe_utils.default.clone(getConfig().grid.proxyConfig, true), props.proxyConfig);
    });
    const computeIsRespMsg = computed(() => {
      const proxyOpts = computeProxyOpts.value;
      return import_xe_utils.default.isBoolean(proxyOpts.message) ? proxyOpts.message : proxyOpts.showResponseMsg;
    });
    const computeIsActiveMsg = computed(() => {
      const proxyOpts = computeProxyOpts.value;
      return proxyOpts.showActiveMsg;
    });
    const computePagerOpts = computed(() => {
      return Object.assign({}, getConfig().grid.pagerConfig, props.pagerConfig);
    });
    const computeFormOpts = computed(() => {
      return Object.assign({}, getConfig().grid.formConfig, props.formConfig);
    });
    const computeToolbarOpts = computed(() => {
      return Object.assign({}, getConfig().grid.toolbarConfig, props.toolbarConfig);
    });
    const computeZoomOpts = computed(() => {
      return Object.assign({}, getConfig().grid.zoomConfig, props.zoomConfig);
    });
    const computeStyles = computed(() => {
      return reactData.isZMax ? { zIndex: reactData.tZindex } : null;
    });
    const computeTableExtendProps = computed(() => {
      const rest = {};
      const gridProps = props;
      tableComponentPropKeys.forEach((key) => {
        rest[key] = gridProps[key];
      });
      return rest;
    });
    const computeTableProps = computed(() => {
      const { seqConfig, pagerConfig, loading, editConfig, proxyConfig } = props;
      const { isZMax, tableLoading, tablePage, tableData } = reactData;
      const tableExtendProps = computeTableExtendProps.value;
      const proxyOpts = computeProxyOpts.value;
      const pagerOpts = computePagerOpts.value;
      const tableProps = Object.assign({}, tableExtendProps);
      if (isZMax) {
        if (tableExtendProps.maxHeight) {
          tableProps.maxHeight = "100%";
        } else {
          tableProps.height = "100%";
        }
      }
      if (proxyConfig && isEnableConf(proxyOpts)) {
        tableProps.loading = loading || tableLoading;
        tableProps.data = tableData;
        if (pagerConfig && proxyOpts.seq && isEnableConf(pagerOpts)) {
          tableProps.seqConfig = Object.assign({}, seqConfig, { startIndex: (tablePage.currentPage - 1) * tablePage.pageSize });
        }
      }
      if (editConfig) {
        tableProps.editConfig = Object.assign({}, editConfig);
      }
      return tableProps;
    });
    const computeCurrLayout = computed(() => {
      const { layouts } = props;
      if (layouts && layouts.length) {
        return layouts;
      }
      return getConfig().grid.layouts || ["Form", "Toolbar", "Top", "Table", "Bottom", "Pager"];
    });
    const computePageConfFlag = computed(() => {
      const pagerOpts = computePagerOpts.value;
      return `${pagerOpts.currentPage}${pagerOpts.pageSize}`;
    });
    const refMaps = {
      refElem,
      refTable,
      refForm,
      refToolbar,
      refPager
    };
    const computeMaps = {
      computeProxyOpts,
      computePagerOpts,
      computeFormOpts,
      computeToolbarOpts,
      computeZoomOpts
    };
    const $xeGrid = {
      xID,
      props,
      context,
      reactData,
      getRefMaps: () => refMaps,
      getComputeMaps: () => computeMaps
    };
    const initToolbar = () => {
      const toolbarOpts = computeToolbarOpts.value;
      if (props.toolbarConfig && isEnableConf(toolbarOpts)) {
        nextTick(() => {
          const $xeTable = refTable.value;
          const $xeToolbar = refToolbar.value;
          if ($xeTable && $xeToolbar) {
            $xeTable.connect($xeToolbar);
          }
        });
      }
    };
    const getFormData = () => {
      const { proxyConfig } = props;
      const { formData } = reactData;
      const proxyOpts = computeProxyOpts.value;
      const formOpts = computeFormOpts.value;
      return proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form ? formData : formOpts.data;
    };
    const initPages = () => {
      const { tablePage } = reactData;
      const { pagerConfig } = props;
      const pagerOpts = computePagerOpts.value;
      const { currentPage, pageSize } = pagerOpts;
      if (pagerConfig && isEnableConf(pagerOpts)) {
        if (currentPage) {
          tablePage.currentPage = currentPage;
        }
        if (pageSize) {
          tablePage.pageSize = pageSize;
        }
      }
    };
    const triggerPendingEvent = (code) => {
      const isActiveMsg = computeIsActiveMsg.value;
      const $xeTable = refTable.value;
      const selectRecords = $xeTable.getCheckboxRecords();
      if (selectRecords.length) {
        $xeTable.togglePendingRow(selectRecords);
        gridExtendTableMethods.clearCheckboxRow();
      } else {
        if (isActiveMsg) {
          if (VxeUI.modal) {
            VxeUI.modal.message({ id: code, content: getI18n("vxe.grid.selectOneRecord"), status: "warning" });
          }
        }
      }
    };
    const getRespMsg = (rest, defaultMsg) => {
      const proxyOpts = computeProxyOpts.value;
      const resConfigs = proxyOpts.response || proxyOpts.props || {};
      const messageProp = resConfigs.message;
      let msg;
      if (rest && messageProp) {
        msg = import_xe_utils.default.isFunction(messageProp) ? messageProp({ data: rest, $grid: $xeGrid }) : import_xe_utils.default.get(rest, messageProp);
      }
      return msg || getI18n(defaultMsg);
    };
    const handleDeleteRow = (code, alertKey, callback) => {
      const isActiveMsg = computeIsActiveMsg.value;
      const selectRecords = gridExtendTableMethods.getCheckboxRecords();
      if (isActiveMsg) {
        if (selectRecords.length) {
          if (VxeUI.modal) {
            return VxeUI.modal.confirm({ id: `cfm_${code}`, content: getI18n(alertKey), escClosable: true }).then((type) => {
              if (type === "confirm") {
                return callback();
              }
            });
          }
        } else {
          if (VxeUI.modal) {
            VxeUI.modal.message({ id: `msg_${code}`, content: getI18n("vxe.grid.selectOneRecord"), status: "warning" });
          }
        }
      } else {
        if (selectRecords.length) {
          callback();
        }
      }
      return Promise.resolve();
    };
    const pageChangeEvent = (params) => {
      const { proxyConfig } = props;
      const { tablePage } = reactData;
      const { $event, currentPage, pageSize } = params;
      const proxyOpts = computeProxyOpts.value;
      tablePage.currentPage = currentPage;
      tablePage.pageSize = pageSize;
      gridMethods.dispatchEvent("page-change", params, $event);
      if (proxyConfig && isEnableConf(proxyOpts)) {
        gridMethods.commitProxy("query").then((rest) => {
          gridMethods.dispatchEvent("proxy-query", rest, $event);
        });
      }
    };
    const sortChangeEvent = (params) => {
      const $xeTable = refTable.value;
      const { proxyConfig } = props;
      const { computeSortOpts } = $xeTable.getComputeMaps();
      const proxyOpts = computeProxyOpts.value;
      const sortOpts = computeSortOpts.value;
      if (sortOpts.remote) {
        reactData.sortData = params.sortList;
        if (proxyConfig && isEnableConf(proxyOpts)) {
          reactData.tablePage.currentPage = 1;
          gridMethods.commitProxy("query").then((rest) => {
            gridMethods.dispatchEvent("proxy-query", rest, params.$event);
          });
        }
      }
      gridMethods.dispatchEvent("sort-change", params, params.$event);
    };
    const filterChangeEvent = (params) => {
      const $xeTable = refTable.value;
      const { proxyConfig } = props;
      const { computeFilterOpts } = $xeTable.getComputeMaps();
      const proxyOpts = computeProxyOpts.value;
      const filterOpts = computeFilterOpts.value;
      if (filterOpts.remote) {
        reactData.filterData = params.filterList;
        if (proxyConfig && isEnableConf(proxyOpts)) {
          reactData.tablePage.currentPage = 1;
          gridMethods.commitProxy("query").then((rest) => {
            gridMethods.dispatchEvent("proxy-query", rest, params.$event);
          });
        }
      }
      gridMethods.dispatchEvent("filter-change", params, params.$event);
    };
    const submitFormEvent = (params) => {
      const { proxyConfig } = props;
      const proxyOpts = computeProxyOpts.value;
      if (proxyConfig && isEnableConf(proxyOpts)) {
        gridMethods.commitProxy("reload").then((rest) => {
          gridMethods.dispatchEvent("proxy-query", Object.assign(Object.assign({}, rest), { isReload: true }), params.$event);
        });
      }
      gridMethods.dispatchEvent("form-submit", params, params.$event);
    };
    const resetFormEvent = (params) => {
      const { proxyConfig } = props;
      const { $event } = params;
      const proxyOpts = computeProxyOpts.value;
      const $xeTable = refTable.value;
      if (proxyConfig && isEnableConf(proxyOpts)) {
        $xeTable.clearScroll();
        gridMethods.commitProxy("reload").then((rest) => {
          gridMethods.dispatchEvent("proxy-query", Object.assign(Object.assign({}, rest), { isReload: true }), $event);
        });
      }
      gridMethods.dispatchEvent("form-reset", params, $event);
    };
    const submitInvalidEvent = (params) => {
      gridMethods.dispatchEvent("form-submit-invalid", params, params.$event);
    };
    const collapseEvent = (params) => {
      const { $event } = params;
      nextTick(() => gridExtendTableMethods.recalculate(true));
      gridMethods.dispatchEvent("form-toggle-collapse", params, $event);
      gridMethods.dispatchEvent("form-collapse", params, $event);
    };
    const handleZoom = (isMax) => {
      const { isZMax } = reactData;
      if (isMax ? !isZMax : isZMax) {
        reactData.isZMax = !isZMax;
        if (reactData.tZindex < getLastZIndex()) {
          reactData.tZindex = nextZIndex();
        }
      }
      return nextTick().then(() => gridExtendTableMethods.recalculate(true)).then(() => reactData.isZMax);
    };
    const getFuncSlot = (optSlots, slotKey) => {
      const funcSlot = optSlots[slotKey];
      if (funcSlot) {
        if (import_xe_utils.default.isString(funcSlot)) {
          if (slots[funcSlot]) {
            return slots[funcSlot];
          } else {
            if (true) {
              errLog("vxe.error.notSlot", [funcSlot]);
            }
          }
        } else {
          return funcSlot;
        }
      }
      return null;
    };
    const renderForm = () => {
      const { formConfig, proxyConfig } = props;
      const { formData } = reactData;
      const proxyOpts = computeProxyOpts.value;
      const formOpts = computeFormOpts.value;
      if (formConfig && isEnableConf(formOpts) || slots.form) {
        let slotVNs = [];
        if (slots.form) {
          slotVNs = slots.form({ $grid: $xeGrid });
        } else {
          if (formOpts.items) {
            const formSlots = {};
            if (!formOpts.inited) {
              formOpts.inited = true;
              const beforeItem = proxyOpts.beforeItem;
              if (proxyOpts && beforeItem) {
                formOpts.items.forEach((item) => {
                  beforeItem({ $grid: $xeGrid, item });
                });
              }
            }
            formOpts.items.forEach((item) => {
              import_xe_utils.default.each(item.slots, (func) => {
                if (!import_xe_utils.default.isFunction(func)) {
                  if (slots[func]) {
                    formSlots[func] = slots[func];
                  }
                }
              });
            });
            if (VxeUIFormComponent) {
              slotVNs.push(h(VxeUIFormComponent, Object.assign(Object.assign({ ref: refForm }, Object.assign({}, formOpts, {
                data: proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form ? formData : formOpts.data
              })), { onSubmit: submitFormEvent, onReset: resetFormEvent, onSubmitInvalid: submitInvalidEvent, onCollapse: collapseEvent }), formSlots));
            }
          }
        }
        return h("div", {
          ref: refFormWrapper,
          key: "form",
          class: "vxe-grid--form-wrapper"
        }, slotVNs);
      }
      return createCommentVNode();
    };
    const renderToolbar = () => {
      const { toolbarConfig } = props;
      const toolbarOpts = computeToolbarOpts.value;
      if (toolbarConfig && isEnableConf(toolbarOpts) || slots.toolbar) {
        let slotVNs = [];
        if (slots.toolbar) {
          slotVNs = slots.toolbar({ $grid: $xeGrid });
        } else {
          const toolbarOptSlots = toolbarOpts.slots;
          let buttonsSlot;
          let toolsSlot;
          const toolbarSlots = {};
          if (toolbarOptSlots) {
            buttonsSlot = getFuncSlot(toolbarOptSlots, "buttons");
            toolsSlot = getFuncSlot(toolbarOptSlots, "tools");
            if (buttonsSlot) {
              toolbarSlots.buttons = buttonsSlot;
            }
            if (toolsSlot) {
              toolbarSlots.tools = toolsSlot;
            }
          }
          slotVNs.push(h(toolbar_default, Object.assign({ ref: refToolbar }, toolbarOpts), toolbarSlots));
        }
        return h("div", {
          ref: refToolbarWrapper,
          key: "toolbar",
          class: "vxe-grid--toolbar-wrapper"
        }, slotVNs);
      }
      return createCommentVNode();
    };
    const renderTop = () => {
      if (slots.top) {
        return h("div", {
          ref: refTopWrapper,
          key: "top",
          class: "vxe-grid--top-wrapper"
        }, slots.top({ $grid: $xeGrid }));
      }
      return createCommentVNode();
    };
    const renderTableLeft = () => {
      const leftSlot = slots.left;
      if (leftSlot) {
        return h("div", {
          class: "vxe-grid--left-wrapper"
        }, leftSlot({ $grid: $xeGrid }));
      }
      return createCommentVNode();
    };
    const renderTableRight = () => {
      const rightSlot = slots.right;
      if (rightSlot) {
        return h("div", {
          class: "vxe-grid--right-wrapper"
        }, rightSlot({ $grid: $xeGrid }));
      }
      return createCommentVNode();
    };
    const renderTable = () => {
      const { proxyConfig } = props;
      const tableProps = computeTableProps.value;
      const proxyOpts = computeProxyOpts.value;
      const tableOns = Object.assign({}, tableCompEvents);
      const emptySlot = slots.empty;
      const loadingSlot = slots.loading;
      if (proxyConfig && isEnableConf(proxyOpts)) {
        if (proxyOpts.sort) {
          tableOns.onSortChange = sortChangeEvent;
        }
        if (proxyOpts.filter) {
          tableOns.onFilterChange = filterChangeEvent;
        }
      }
      const slotObj = {};
      if (emptySlot) {
        slotObj.empty = () => emptySlot({ $grid: $xeGrid });
      }
      if (loadingSlot) {
        slotObj.loading = () => loadingSlot({ $grid: $xeGrid });
      }
      return h("div", {
        class: "vxe-grid--table-wrapper"
      }, [
        h(table_default, Object.assign(Object.assign({ ref: refTable }, tableProps), tableOns), slotObj)
      ]);
    };
    const renderBottom = () => {
      if (slots.bottom) {
        return h("div", {
          ref: refBottomWrapper,
          key: "bottom",
          class: "vxe-grid--bottom-wrapper"
        }, slots.bottom({ $grid: $xeGrid }));
      }
      return createCommentVNode();
    };
    const renderPager = () => {
      const { proxyConfig, pagerConfig } = props;
      const proxyOpts = computeProxyOpts.value;
      const pagerOpts = computePagerOpts.value;
      if (pagerConfig && isEnableConf(pagerOpts) || slots.pager) {
        let slotVNs = [];
        if (slots.pager) {
          slotVNs = slots.pager({ $grid: $xeGrid });
        } else {
          const pagerOptSlots = pagerOpts.slots;
          const pagerSlots = {};
          let leftSlot;
          let rightSlot;
          if (pagerOptSlots) {
            leftSlot = getFuncSlot(pagerOptSlots, "left");
            rightSlot = getFuncSlot(pagerOptSlots, "right");
            if (leftSlot) {
              pagerSlots.left = leftSlot;
            }
            if (rightSlot) {
              pagerSlots.right = rightSlot;
            }
          }
          if (VxeUIPagerComponent) {
            slotVNs.push(h(VxeUIPagerComponent, Object.assign(Object.assign(Object.assign({ ref: refPager }, pagerOpts), proxyConfig && isEnableConf(proxyOpts) ? reactData.tablePage : {}), { onPageChange: pageChangeEvent }), pagerSlots));
          }
        }
        return h("div", {
          ref: refPagerWrapper,
          key: "pager",
          class: "vxe-grid--pager-wrapper"
        }, slotVNs);
      }
      return createCommentVNode();
    };
    const renderLayout = () => {
      const vns = [];
      const currLayouts = computeCurrLayout.value;
      currLayouts.forEach((name) => {
        switch (name) {
          case "Form":
            vns.push(renderForm());
            break;
          case "Toolbar":
            vns.push(renderToolbar());
            break;
          case "Top":
            vns.push(renderTop());
            break;
          case "Table":
            vns.push(h("div", {
              key: "table",
              class: "vxe-grid--table-container"
            }, [
              renderTableLeft(),
              renderTable(),
              renderTableRight()
            ]));
            break;
          case "Bottom":
            vns.push(renderBottom());
            break;
          case "Pager":
            vns.push(renderPager());
            break;
          default:
            if (true) {
              errLog("vxe.error.notProp", [`layouts -> ${name}`]);
            }
            break;
        }
      });
      return vns;
    };
    const tableCompEvents = {};
    emits_default.forEach((name) => {
      const type = import_xe_utils.default.camelCase(`on-${name}`);
      tableCompEvents[type] = (...args) => emit(name, ...args);
    });
    const initProxy = () => {
      const { proxyConfig, formConfig } = props;
      const { proxyInited } = reactData;
      const proxyOpts = computeProxyOpts.value;
      const formOpts = computeFormOpts.value;
      if (proxyConfig && isEnableConf(proxyOpts)) {
        if (formConfig && isEnableConf(formOpts) && proxyOpts.form && formOpts.items) {
          const fData = {};
          formOpts.items.forEach((item) => {
            const { field, itemRender } = item;
            if (field) {
              let itemValue = null;
              if (itemRender) {
                const { defaultValue } = itemRender;
                if (import_xe_utils.default.isFunction(defaultValue)) {
                  itemValue = defaultValue({ item });
                } else if (!import_xe_utils.default.isUndefined(defaultValue)) {
                  itemValue = defaultValue;
                }
              }
              fData[field] = itemValue;
            }
          });
          reactData.formData = fData;
        }
        if (!proxyInited) {
          reactData.proxyInited = true;
          if (proxyOpts.autoLoad !== false) {
            nextTick().then(() => gridMethods.commitProxy("_init")).then((rest) => {
              gridMethods.dispatchEvent("proxy-query", Object.assign(Object.assign({}, rest), { isInited: true }), new Event("init"));
            });
          }
        }
      }
    };
    const handleGlobalKeydownEvent = (evnt) => {
      const zoomOpts = computeZoomOpts.value;
      const isEsc = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ESCAPE);
      if (isEsc && reactData.isZMax && zoomOpts.escRestore !== false) {
        gridPrivateMethods.triggerZoomEvent(evnt);
      }
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $grid: $xeGrid }, params));
    };
    const gridMethods = {
      dispatchEvent,
      /**
       * 提交指令，支持 code 或 button
       * @param {String/Object} code 字符串或对象
       */
      commitProxy(proxyTarget, ...args) {
        const { toolbarConfig, pagerConfig, editRules, validConfig } = props;
        const { tablePage } = reactData;
        const isActiveMsg = computeIsActiveMsg.value;
        const isRespMsg = computeIsRespMsg.value;
        const proxyOpts = computeProxyOpts.value;
        const pagerOpts = computePagerOpts.value;
        const toolbarOpts = computeToolbarOpts.value;
        const { beforeQuery, afterQuery, beforeDelete, afterDelete, beforeSave, afterSave, ajax = {} } = proxyOpts;
        const resConfigs = proxyOpts.response || proxyOpts.props || {};
        const $xeTable = refTable.value;
        const formData = getFormData();
        let button = null;
        let code = null;
        if (import_xe_utils.default.isString(proxyTarget)) {
          const { buttons } = toolbarOpts;
          const matchObj = toolbarConfig && isEnableConf(toolbarOpts) && buttons ? import_xe_utils.default.findTree(buttons, (item) => item.code === proxyTarget, { children: "dropdowns" }) : null;
          button = matchObj ? matchObj.item : null;
          code = proxyTarget;
        } else {
          button = proxyTarget;
          code = button.code;
        }
        const btnParams = button ? button.params : null;
        switch (code) {
          case "insert":
            return $xeTable.insert({});
          case "insert_edit":
            return $xeTable.insert({}).then(({ row }) => $xeTable.setEditRow(row));
          // 已废弃
          case "insert_actived":
            return $xeTable.insert({}).then(({ row }) => $xeTable.setEditRow(row));
          // 已废弃
          case "mark_cancel":
            triggerPendingEvent(code);
            break;
          case "remove":
            return handleDeleteRow(code, "vxe.grid.removeSelectRecord", () => $xeTable.removeCheckboxRow());
          case "import":
            $xeTable.importData(btnParams);
            break;
          case "open_import":
            $xeTable.openImport(btnParams);
            break;
          case "export":
            $xeTable.exportData(btnParams);
            break;
          case "open_export":
            $xeTable.openExport(btnParams);
            break;
          case "reset_custom":
            return $xeTable.resetCustom(true);
          case "_init":
          case "reload":
          case "query": {
            const ajaxMethods = ajax.query;
            const querySuccessMethods = ajax.querySuccess;
            const queryErrorMethods = ajax.queryError;
            if (ajaxMethods) {
              const isInited = code === "_init";
              const isReload = code === "reload";
              let sortList = [];
              let filterList = [];
              let pageParams = {};
              if (pagerConfig) {
                if (isInited || isReload) {
                  tablePage.currentPage = 1;
                }
                if (isEnableConf(pagerOpts)) {
                  pageParams = Object.assign({}, tablePage);
                }
              }
              if (isInited) {
                let defaultSort = null;
                if ($xeTable) {
                  const { computeSortOpts } = $xeTable.getComputeMaps();
                  const sortOpts = computeSortOpts.value;
                  defaultSort = sortOpts.defaultSort;
                }
                if (defaultSort) {
                  if (!import_xe_utils.default.isArray(defaultSort)) {
                    defaultSort = [defaultSort];
                  }
                  sortList = defaultSort.map((item) => {
                    return {
                      field: item.field,
                      property: item.field,
                      order: item.order
                    };
                  });
                }
                if ($xeTable) {
                  filterList = $xeTable.getCheckedFilters();
                }
              } else {
                if ($xeTable) {
                  if (isReload) {
                    $xeTable.clearAll();
                  } else {
                    sortList = $xeTable.getSortColumns();
                    filterList = $xeTable.getCheckedFilters();
                  }
                }
              }
              const commitParams = {
                code,
                button,
                isInited,
                isReload,
                $grid: $xeGrid,
                page: pageParams,
                sort: sortList.length ? sortList[0] : {},
                sorts: sortList,
                filters: filterList,
                form: formData,
                options: ajaxMethods
              };
              reactData.sortData = sortList;
              reactData.filterData = filterList;
              reactData.tableLoading = true;
              return Promise.resolve((beforeQuery || ajaxMethods)(commitParams, ...args)).then((rest) => {
                reactData.tableLoading = false;
                if (rest) {
                  if (pagerConfig && isEnableConf(pagerOpts)) {
                    const totalProp = resConfigs.total;
                    const total = (import_xe_utils.default.isFunction(totalProp) ? totalProp({ data: rest, $grid: $xeGrid }) : import_xe_utils.default.get(rest, totalProp || "page.total")) || 0;
                    tablePage.total = import_xe_utils.default.toNumber(total);
                    const resultProp = resConfigs.result;
                    reactData.tableData = (import_xe_utils.default.isFunction(resultProp) ? resultProp({ data: rest, $grid: $xeGrid }) : import_xe_utils.default.get(rest, resultProp || "result")) || [];
                    const pageCount = Math.max(Math.ceil(total / tablePage.pageSize), 1);
                    if (tablePage.currentPage > pageCount) {
                      tablePage.currentPage = pageCount;
                    }
                  } else {
                    const listProp = resConfigs.list;
                    reactData.tableData = (listProp ? import_xe_utils.default.isFunction(listProp) ? listProp({ data: rest, $grid: $xeGrid }) : import_xe_utils.default.get(rest, listProp) : rest) || [];
                  }
                } else {
                  reactData.tableData = [];
                }
                if (afterQuery) {
                  afterQuery(commitParams, ...args);
                }
                if (querySuccessMethods) {
                  querySuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                }
                return { status: true };
              }).catch((rest) => {
                reactData.tableLoading = false;
                if (queryErrorMethods) {
                  queryErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                }
                return { status: false };
              });
            } else {
              if (true) {
                errLog("vxe.error.notFunc", ["proxy-config.ajax.query"]);
              }
            }
            break;
          }
          case "delete": {
            const ajaxMethods = ajax.delete;
            const deleteSuccessMethods = ajax.deleteSuccess;
            const deleteErrorMethods = ajax.deleteError;
            if (ajaxMethods) {
              const selectRecords = gridExtendTableMethods.getCheckboxRecords();
              const removeRecords = selectRecords.filter((row) => !$xeTable.isInsertByRow(row));
              const body = { removeRecords };
              const commitParams = { $grid: $xeGrid, code, button, body, form: formData, options: ajaxMethods };
              if (selectRecords.length) {
                return handleDeleteRow(code, "vxe.grid.deleteSelectRecord", () => {
                  if (!removeRecords.length) {
                    return $xeTable.remove(selectRecords);
                  }
                  reactData.tableLoading = true;
                  return Promise.resolve((beforeDelete || ajaxMethods)(commitParams, ...args)).then((rest) => {
                    reactData.tableLoading = false;
                    $xeTable.setPendingRow(removeRecords, false);
                    if (isRespMsg) {
                      if (VxeUI.modal) {
                        VxeUI.modal.message({ content: getRespMsg(rest, "vxe.grid.delSuccess"), status: "success" });
                      }
                    }
                    if (afterDelete) {
                      afterDelete(commitParams, ...args);
                    } else {
                      gridMethods.commitProxy("query");
                    }
                    if (deleteSuccessMethods) {
                      deleteSuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                    }
                    return { status: true };
                  }).catch((rest) => {
                    reactData.tableLoading = false;
                    if (isRespMsg) {
                      if (VxeUI.modal) {
                        VxeUI.modal.message({ id: code, content: getRespMsg(rest, "vxe.grid.operError"), status: "error" });
                      }
                    }
                    if (deleteErrorMethods) {
                      deleteErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                    }
                    return { status: false };
                  });
                });
              } else {
                if (isActiveMsg) {
                  if (VxeUI.modal) {
                    VxeUI.modal.message({ id: code, content: getI18n("vxe.grid.selectOneRecord"), status: "warning" });
                  }
                }
              }
            } else {
              if (true) {
                errLog("vxe.error.notFunc", ["proxy-config.ajax.delete"]);
              }
            }
            break;
          }
          case "save": {
            const ajaxMethods = ajax.save;
            const saveSuccessMethods = ajax.saveSuccess;
            const saveErrorMethods = ajax.saveError;
            if (ajaxMethods) {
              const body = $xeTable.getRecordset();
              const { insertRecords, removeRecords, updateRecords, pendingRecords } = body;
              const commitParams = { $grid: $xeGrid, code, button, body, form: formData, options: ajaxMethods };
              if (insertRecords.length) {
                body.pendingRecords = pendingRecords.filter((row) => $xeTable.findRowIndexOf(insertRecords, row) === -1);
              }
              if (pendingRecords.length) {
                body.insertRecords = insertRecords.filter((row) => $xeTable.findRowIndexOf(pendingRecords, row) === -1);
              }
              let restPromise = Promise.resolve();
              if (editRules) {
                restPromise = $xeTable[validConfig && validConfig.msgMode === "full" ? "fullValidate" : "validate"](body.insertRecords.concat(updateRecords));
              }
              return restPromise.then((errMap) => {
                if (errMap) {
                  return;
                }
                if (body.insertRecords.length || removeRecords.length || updateRecords.length || body.pendingRecords.length) {
                  reactData.tableLoading = true;
                  return Promise.resolve((beforeSave || ajaxMethods)(commitParams, ...args)).then((rest) => {
                    reactData.tableLoading = false;
                    $xeTable.clearPendingRow();
                    if (isRespMsg) {
                      if (VxeUI.modal) {
                        VxeUI.modal.message({ content: getRespMsg(rest, "vxe.grid.saveSuccess"), status: "success" });
                      }
                    }
                    if (afterSave) {
                      afterSave(commitParams, ...args);
                    } else {
                      gridMethods.commitProxy("query");
                    }
                    if (saveSuccessMethods) {
                      saveSuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                    }
                    return { status: true };
                  }).catch((rest) => {
                    reactData.tableLoading = false;
                    if (isRespMsg) {
                      if (VxeUI.modal) {
                        VxeUI.modal.message({ id: code, content: getRespMsg(rest, "vxe.grid.operError"), status: "error" });
                      }
                    }
                    if (saveErrorMethods) {
                      saveErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));
                    }
                    return { status: false };
                  });
                } else {
                  if (isActiveMsg) {
                    if (VxeUI.modal) {
                      VxeUI.modal.message({ id: code, content: getI18n("vxe.grid.dataUnchanged"), status: "info" });
                    }
                  }
                }
              });
            } else {
              if (true) {
                errLog("vxe.error.notFunc", ["proxy-config.ajax.save"]);
              }
            }
            break;
          }
          default: {
            const gCommandOpts = commands.get(code);
            if (gCommandOpts) {
              const tCommandMethod = gCommandOpts.tableCommandMethod || gCommandOpts.commandMethod;
              if (tCommandMethod) {
                tCommandMethod({ code, button, $grid: $xeGrid, $table: $xeTable }, ...args);
              } else {
                if (true) {
                  errLog("vxe.error.notCommands", [code]);
                }
              }
            }
          }
        }
        return nextTick();
      },
      zoom() {
        if (reactData.isZMax) {
          return gridMethods.revert();
        }
        return gridMethods.maximize();
      },
      isMaximized() {
        return reactData.isZMax;
      },
      maximize() {
        return handleZoom(true);
      },
      revert() {
        return handleZoom();
      },
      getFormData,
      getFormItems(itemIndex) {
        const formOpts = computeFormOpts.value;
        const { formConfig } = props;
        const { items } = formOpts;
        const itemList = [];
        import_xe_utils.default.eachTree(formConfig && isEnableConf(formOpts) && items ? items : [], (item) => {
          itemList.push(item);
        }, { children: "children" });
        return import_xe_utils.default.isUndefined(itemIndex) ? itemList : itemList[itemIndex];
      },
      getProxyInfo() {
        const $xeTable = refTable.value;
        if (props.proxyConfig) {
          const { sortData } = reactData;
          return {
            data: reactData.tableData,
            filter: reactData.filterData,
            form: getFormData(),
            sort: sortData.length ? sortData[0] : {},
            sorts: sortData,
            pager: reactData.tablePage,
            pendingRecords: $xeTable ? $xeTable.getPendingRecords() : []
          };
        }
        return null;
      }
      // setProxyInfo (options) {
      //   if (props.proxyConfig && options) {
      //     const { pager, form } = options
      //     const proxyOpts = computeProxyOpts.value
      //     if (pager) {
      //       if (pager.currentPage) {
      //         reactData.tablePage.currentPage = Number(pager.currentPage)
      //       }
      //       if (pager.pageSize) {
      //         reactData.tablePage.pageSize = Number(pager.pageSize)
      //       }
      //     }
      //     if (proxyOpts.form && form) {
      //       Object.assign(reactData.formData, form)
      //     }
      //   }
      //   return nextTick()
      // }
    };
    if (true) {
      gridMethods.loadColumn = (columns) => {
        const $xeTable = refTable.value;
        import_xe_utils.default.eachTree(columns, (column) => {
          if (column.slots) {
            import_xe_utils.default.each(column.slots, (func) => {
              if (!import_xe_utils.default.isFunction(func)) {
                if (!slots[func]) {
                  errLog("vxe.error.notSlot", [func]);
                }
              }
            });
          }
        });
        if ($xeTable) {
          return $xeTable.loadColumn(columns);
        }
        return nextTick();
      };
      gridMethods.reloadColumn = (columns) => {
        gridExtendTableMethods.clearAll();
        return gridMethods.loadColumn(columns);
      };
    }
    const gridPrivateMethods = {
      extendTableMethods,
      callSlot(slotFunc, params) {
        if (slotFunc) {
          if (import_xe_utils.default.isString(slotFunc)) {
            slotFunc = slots[slotFunc] || null;
          }
          if (import_xe_utils.default.isFunction(slotFunc)) {
            return getSlotVNs(slotFunc(params));
          }
        }
        return [];
      },
      /**
       * 获取需要排除的高度
       */
      getExcludeHeight() {
        const { height } = props;
        const { isZMax } = reactData;
        const el = refElem.value;
        const formWrapper = refFormWrapper.value;
        const toolbarWrapper = refToolbarWrapper.value;
        const topWrapper = refTopWrapper.value;
        const bottomWrapper = refBottomWrapper.value;
        const pagerWrapper = refPagerWrapper.value;
        const parentPaddingSize = isZMax || !(height === "auto" || height === "100%") ? 0 : getPaddingTopBottomSize(el.parentNode);
        return parentPaddingSize + getPaddingTopBottomSize(el) + getOffsetHeight(formWrapper) + getOffsetHeight(toolbarWrapper) + getOffsetHeight(topWrapper) + getOffsetHeight(bottomWrapper) + getOffsetHeight(pagerWrapper);
      },
      getParentHeight() {
        const el = refElem.value;
        if (el) {
          return (reactData.isZMax ? getDomNode().visibleHeight : import_xe_utils.default.toNumber(getComputedStyle(el.parentNode).height)) - gridPrivateMethods.getExcludeHeight();
        }
        return 0;
      },
      triggerToolbarCommitEvent(params, evnt) {
        const { code } = params;
        return gridMethods.commitProxy(params, evnt).then((rest) => {
          if (code && rest && rest.status && ["query", "reload", "delete", "save"].includes(code)) {
            gridMethods.dispatchEvent(code === "delete" || code === "save" ? `proxy-${code}` : "proxy-query", Object.assign(Object.assign({}, rest), { isReload: code === "reload" }), evnt);
          }
        });
      },
      triggerToolbarBtnEvent(button, evnt) {
        gridPrivateMethods.triggerToolbarCommitEvent(button, evnt);
        gridMethods.dispatchEvent("toolbar-button-click", { code: button.code, button }, evnt);
      },
      triggerToolbarTolEvent(tool, evnt) {
        gridPrivateMethods.triggerToolbarCommitEvent(tool, evnt);
        gridMethods.dispatchEvent("toolbar-tool-click", { code: tool.code, tool }, evnt);
      },
      triggerZoomEvent(evnt) {
        gridMethods.zoom();
        gridMethods.dispatchEvent("zoom", { type: reactData.isZMax ? "max" : "revert" }, evnt);
      }
    };
    Object.assign($xeGrid, gridExtendTableMethods, gridMethods, gridPrivateMethods);
    const columnFlag = ref(0);
    watch(() => props.columns ? props.columns.length : -1, () => {
      columnFlag.value++;
    });
    watch(() => props.columns, () => {
      columnFlag.value++;
    });
    watch(columnFlag, () => {
      nextTick(() => $xeGrid.loadColumn(props.columns || []));
    });
    watch(() => props.toolbarConfig, () => {
      initToolbar();
    });
    watch(computePageConfFlag, () => {
      initPages();
    });
    watch(() => props.proxyConfig, () => {
      initProxy();
    });
    hooks.forEach((options) => {
      const { setupGrid } = options;
      if (setupGrid) {
        const hookRest = setupGrid($xeGrid);
        if (hookRest && import_xe_utils.default.isObject(hookRest)) {
          Object.assign($xeGrid, hookRest);
        }
      }
    });
    initPages();
    onMounted(() => {
      if (true) {
        nextTick(() => {
          if (props.formConfig) {
            if (!VxeUIFormComponent) {
              errLog("vxe.error.reqComp", ["vxe-form"]);
            }
          }
          if (props.pagerConfig) {
            if (!VxeUIPagerComponent) {
              errLog("vxe.error.reqComp", ["vxe-pager"]);
            }
          }
        });
      }
      nextTick(() => {
        const { columns } = props;
        if (columns && columns.length) {
          $xeGrid.loadColumn(columns);
        }
        initToolbar();
        initProxy();
      });
      globalEvents.on($xeGrid, "keydown", handleGlobalKeydownEvent);
    });
    onUnmounted(() => {
      globalEvents.off($xeGrid, "keydown");
    });
    const renderVN = () => {
      const vSize = computeSize.value;
      const styles = computeStyles.value;
      return h("div", {
        ref: refElem,
        class: ["vxe-grid", {
          [`size--${vSize}`]: vSize,
          "is--animat": !!props.animat,
          "is--round": props.round,
          "is--maximize": reactData.isZMax,
          "is--loading": props.loading || reactData.tableLoading
        }],
        style: styles
      }, renderLayout());
    };
    $xeGrid.renderVN = renderVN;
    provide("$xeGrid", $xeGrid);
    return $xeGrid;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/grid/index.js
var VxeGrid = Object.assign({}, grid_default, {
  install(app) {
    app.component(grid_default.name, grid_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(grid_default.name, grid_default);
}
VxeUI.component(grid_default);
var Grid = VxeGrid;
var grid_default2 = VxeGrid;

// ../../node_modules/.pnpm/vxe-table@4.10.0_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-table/es/vxe-grid/index.js
var vxe_grid_default = grid_default2;
export {
  Grid,
  VxeGrid,
  vxe_grid_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-grid_index__js.js.map
